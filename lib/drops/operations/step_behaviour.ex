defmodule Drops.Operations.StepBehaviour do
  @moduledoc """
  Defines common callback specifications for operation step functions.

  This module provides @callback definitions for the standard step functions
  used throughout the Operations framework. These callbacks can be implemented
  by operations and extensions to ensure consistent function signatures.

  ## Common Step Functions

  - `conform/1` - Validates and transforms input parameters against a schema
  - `prepare/1` - Prepares the context for validation and execution
  - `validate/1` - Validates the prepared context
  - `execute/1` - Executes the main operation logic
  - `changeset/1` - Creates and validates Ecto changesets (Ecto extension)

  ## Context Structure

  All step functions receive a context map that typically contains:

  - `:params` - The input parameters for the operation
  - `:changeset` - An Ecto changeset (when using Ecto extension)
  - Additional keys may be added by extensions or previous steps

  ## Return Values

  All step functions should return either:

  - `{:ok, updated_context}` - Success with updated context
  - `{:error, error}` - Failure with error details
  """

  @type context :: map()
  @type step_result :: {:ok, context()} | {:error, any()}

  @doc """
  Conforms input parameters against the operation's schema.

  This step validates the input parameters and transforms them according
  to the defined schema rules. It's typically the first step in the pipeline.

  ## Parameters

  - `context` - A map containing `:params` key with input parameters

  ## Returns

  - `{:ok, context}` - Success with conformed parameters in context
  - `{:error, error}` - Validation failure with error details
  """
  @callback conform(context()) :: step_result()

  @doc """
  Prepares the context for validation and execution.

  This step can modify the context, fetch additional data, or perform
  any setup required before validation and execution.

  ## Parameters

  - `context` - The current operation context

  ## Returns

  - `{:ok, context}` - Success with prepared context
  - `{:error, error}` - Preparation failure
  """
  @callback prepare(context()) :: step_result()

  @doc """
  Validates the prepared context.

  This step performs business logic validation on the prepared context.
  It can check business rules, data consistency, or any other validation
  requirements.

  ## Parameters

  - `context` - The prepared operation context

  ## Returns

  - `{:ok, context}` - Success with validated context
  - `{:error, error}` - Validation failure
  """
  @callback validate(context()) :: step_result()

  @doc """
  Executes the main operation logic.

  This is the core step that performs the actual work of the operation.
  It receives the validated context and produces the final result.

  ## Parameters

  - `context` - The validated operation context

  ## Returns

  - `{:ok, result}` - Success with operation result
  - `{:error, error}` - Execution failure
  """
  @callback execute(context()) :: step_result()

  @doc """
  Creates and validates an Ecto changeset.

  This step is specific to the Ecto extension and creates a changeset
  from the operation parameters and validates it according to the schema.

  ## Parameters

  - `context` - The operation context containing `:params`

  ## Returns

  - `{:ok, context}` - Success with `:changeset` added to context
  - `{:error, error}` - Changeset creation or validation failure
  """
  @callback changeset(context()) :: step_result()

  @optional_callbacks conform: 1, prepare: 1, validate: 1, execute: 1, changeset: 1
end
